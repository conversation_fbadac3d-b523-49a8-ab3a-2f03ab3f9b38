["tests/test_arinc_utilities.py::TestArincUtilities::test_deterministic_encoding", "tests/test_arinc_utilities.py::TestArincUtilities::test_different_inputs_different_outputs", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_char", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_char2", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_char3", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_flight_info", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_flight_info_empty_strings", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_flight_info_long_strings", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_flight_info_padding", "tests/test_arinc_utilities.py::TestArincUtilities::test_encode_flight_info_special_characters", "tests/test_config.py::TestConfigManager::test_boolean_values", "tests/test_config.py::TestConfigManager::test_channel_config", "tests/test_config.py::TestConfigManager::test_config_creation", "tests/test_config.py::TestConfigManager::test_config_save_load", "tests/test_config.py::TestConfigManager::test_config_validation", "tests/test_config.py::TestConfigManager::test_label_mapping", "tests/test_flight_model.py::TestFlightModel::test_compass_bearing", "tests/test_flight_model.py::TestFlightModel::test_continuous_noise", "tests/test_flight_model.py::TestFlightModel::test_deterministic_behavior", "tests/test_flight_model.py::TestFlightModel::test_flight_phases", "tests/test_flight_model.py::TestFlightModel::test_haversine_distance", "tests/test_flight_model.py::TestFlightModel::test_invalid_airports", "tests/test_flight_model.py::TestFlightModel::test_movement_margin_effect", "tests/test_flight_model.py::TestFlightModel::test_progress_calculation", "tests/test_flight_model.py::TestFlightModel::test_simulate_flight_data_basic"]