"""
Flight Data Structure Module

This module defines unified data structures for storing flight parameters
with both readable values and correctly formatted ARINC 429 values.
"""

from dataclasses import dataclass
from typing import Dict, Any, Union


# ARINC 429 encoding parameters for different parameter types
ARINC_ENCODING_PARAMS = {
    'latitude':             {'type': 'bnr', 'sig_bits': 20, 'range_max': 180.0, 'unit': 'degrees', 'description': 'Aircraft Latitude'},
    'longitude':            {'type': 'bnr', 'sig_bits': 20, 'range_max': 180.0, 'unit': 'degrees', 'description': 'Aircraft Longitude'},
    'altitude':             {'type': 'bcd', 'sig_bits': 17, 'resolution': 1.0, 'unit': 'ft', 'description': 'Aircraft Altitude'},
    'ground_speed':         {'type': 'bnr', 'sig_bits': 15, 'range_max': 4096, 'unit': 'knots', 'description': 'Ground Speed'},
    'true_air_speed':       {'type': 'bcd', 'sig_bits': 15, 'resolution': 0.1, 'unit': 'knots', 'description': 'True Air Speed'},
    'heading':              {'type': 'bcd', 'sig_bits': 15, 'resolution': 0.1, 'unit': 'degrees', 'description': 'Aircraft Heading'},
    'temperature':          {'type': 'bcd', 'sig_bits': 11, 'resolution': 0.1, 'unit': '°C', 'description': 'Static Air Temperature'},
    'time_to_go':           {'type': 'bcd', 'sig_bits': 17, 'resolution': 1.0, 'unit': 'seconds', 'description': 'Estimated Time of Arrival'},
    'total_flight_time':    {'type': 'bcd', 'sig_bits': 17, 'resolution': 1.0, 'unit': 'seconds', 'description': 'Total Flight Time'},
    'distance_nm':          {'type': 'bcd', 'sig_bits': 15, 'resolution': 0.1, 'unit': 'nm', 'description': 'Distance to Destination'},
    'progress':             {'type': 'bcd', 'sig_bits': 10, 'resolution': 0.001, 'unit': '', 'description': 'Flight Progress'},
}

# Mapping from flight data keys to parameter names and ARINC encoding
FLIGHT_DATA_MAPPING = {
    'latitude': 'Latitude_of_aircraft',
    'longitude': 'Longitude_of_aircraft',
    'altitude': 'Altitude_of_aircraft_ft',
    'ground_speed': 'Ground_Speed_knots',
    'true_air_speed': 'True_Air_Speed_knots',
    'heading': 'Heading_degrees',
    'time_to_go': 'ETA_seconds',
    'total_flight_time': 'Total_Flight_Time_seconds',
    'distance_nm': 'Distance_nm',
    'progress': 'progress',
}


@dataclass
class ArincParameter:
    """
    Represents a single ARINC parameter with both readable and encoded values.
    
    Attributes:
        readable_value: Human-readable value (e.g., 35000.0 for altitude)
        arinc_value: ARINC 429 formatted value for PBA Pro labels
        unit: Unit of measurement (e.g., "ft", "knots", "degrees")
        description: Human-readable description
    """
    readable_value: Union[float, int, str]
    arinc_value: Union[float, int]
    unit: str = ""
    description: str = ""


@dataclass
class FlightDataSet:
    """
    Unified data structure containing all flight parameters and ARINC words.

    This structure eliminates redundant calculations by storing all values
    in one place with both readable and ARINC-formatted representations.
    """
    # Flight parameters
    parameters: Dict[str, ArincParameter]

    # Flight info parameters (flight number and city pair)
    flight_info: Dict[str, ArincParameter]

    # Label mapping for PBA Pro
    label_values: Dict[str, Union[float, int]]

    def __post_init__(self):
        """Initialize empty dictionaries if not provided."""
        if self.parameters is None:
            self.parameters = {}
        if self.flight_info is None:
            self.flight_info = {}
        if self.label_values is None:
            self.label_values = {}
    
    @classmethod
    def create_from_flight_data(cls,
                               flight_data: Dict[str, Any],
                               flight_number: str,
                               city_pair: str,
                               label_mapping: Dict[str, Any],
                               config_manager) -> 'FlightDataSet':
        """
        Create a FlightDataSet from raw flight data and configuration.

        Args:
            flight_data: Raw flight simulation data
            flight_number: Flight number for encoding
            city_pair: City pair for encoding
            label_mapping: Label configuration mapping
            config_manager: Configuration manager instance

        Returns:
            FlightDataSet with all values calculated once
        """
        from ..arinc.utilities import encode_flight_info, encode_arinc

        # Calculate temperature once
        temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2

        # Create extended flight data with calculated values
        extended_flight_data = flight_data.copy()
        extended_flight_data['temperature'] = temperature_value

        # Create parameters using the unified helper function
        parameters = {}

        # Process all standard parameters
        for param_name, flight_data_key in FLIGHT_DATA_MAPPING.items():
            if flight_data_key in extended_flight_data:
                parameters[param_name] = cls._create_parameter(
                    param_name,
                    extended_flight_data[flight_data_key],
                    ARINC_ENCODING_PARAMS[param_name]
                )

        # Handle flight phase separately (string parameter)
        if "Flight_Phase" in flight_data:
            parameters['flight_phase'] = ArincParameter(
                readable_value=flight_data["Flight_Phase"],
                arinc_value=hash(flight_data["Flight_Phase"]) & 0xFFFF,  # 16-bit hash
                unit="",
                description="Flight Phase"
            )

        # Handle temperature parameter
        parameters['temperature'] = cls._create_parameter(
            'temperature',
            temperature_value,
            ARINC_ENCODING_PARAMS['temperature']
        )

        # Generate ARINC words for flight info
        arinc_words = encode_flight_info(flight_number, city_pair)

        # Create flight info parameters
        flight_info = cls._create_flight_info_parameters(flight_number, city_pair, arinc_words)

        # Create extended flight data for label mapping
        extended_flight_data['Static_Air_Temperature'] = temperature_value

        # Add encoded flight info values to extended data
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                extended_flight_data[f"FlightNr{flight_nr_index}"] = value
            elif "CityPair" in key:
                city_pair_index = key.split(":")[0].replace("CityPair", "")
                extended_flight_data[f"CityPair{city_pair_index}"] = value

        # Generate label values for PBA Pro
        label_values = {}
        for label_name, (_, _, simulated_value) in label_mapping.items():
            flight_data_key = config_manager.get_flight_data_key(simulated_value)
            if flight_data_key in extended_flight_data:
                label_values[label_name] = extended_flight_data[flight_data_key]
            else:
                label_values[label_name] = 0.0

        return cls(
            parameters=parameters,
            flight_info=flight_info,
            label_values=label_values
        )

    @staticmethod
    def _create_parameter(param_name: str, value: Union[float, int], encoding_params: Dict[str, Any]) -> ArincParameter:
        """
        Create an ArincParameter with ARINC encoding.

        Args:
            param_name: Name of the parameter
            value: Raw value to encode
            encoding_params: Encoding configuration from ARINC_ENCODING_PARAMS

        Returns:
            ArincParameter with encoded ARINC value
        """
        from ..arinc.utilities import encode_arinc

        # Encode the value using the appropriate method
        if encoding_params['type'] == 'bnr':
            arinc_word = encode_arinc(
                value,
                encoding_params['type'],
                encoding_params['sig_bits'],
                encoding_params['range_max']
            )
        elif encoding_params['type'] == 'bcd':
            arinc_word = encode_arinc(
                value,
                encoding_params['type'],
                encoding_params['sig_bits'],
                resolution=encoding_params['resolution']
            )
        else:
            # Fallback for unknown types
            arinc_word = int(value) & 0x1FFFFF

        return ArincParameter(
            readable_value=value,
            arinc_value=arinc_word & 0x1FFFFF,  # Extract data bits (19 bits)
            unit=encoding_params.get('unit', ''),
            description=encoding_params.get('description', param_name.title())
        )

    @staticmethod
    def _create_flight_info_parameters(flight_number: str, city_pair: str, arinc_words: Dict[str, int]) -> Dict[str, ArincParameter]:
        """
        Create flight info parameters from encoded ARINC words.

        Args:
            flight_number: Flight number string
            city_pair: City pair string
            arinc_words: Encoded ARINC words from encode_flight_info

        Returns:
            Dictionary of flight info ArincParameters
        """
        flight_info = {}
        flight_number_padded = flight_number.ljust(6)
        city_pair_padded = city_pair.ljust(6)

        for key, value in arinc_words.items():
            if "FlightNr" in key:
                index = int(key.split(":")[0].replace("FlightNr", ""))
                chars = flight_number_padded[index*2:(index*2)+2]
                flight_info[f"flight_nr_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"Flight Number Part {index} ({chars.strip()})"
                )
            elif "CityPair" in key:
                index = int(key.split(":")[0].replace("CityPair", ""))
                chars = city_pair_padded[index*3:(index*3)+3]
                flight_info[f"city_pair_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"City Pair Part {index} ({chars.strip()})"
                )

        return flight_info

    def get_parameter_by_name(self, name: str) -> ArincParameter:
        """Get a parameter by its name."""
        name_lower = name.lower()
        if name_lower in self.parameters:
            return self.parameters[name_lower]
        elif name_lower in self.flight_info:
            return self.flight_info[name_lower]
        return None

    def get_flight_info_words_dict(self) -> Dict[str, int]:
        """Get flight info words as a dictionary for backward compatibility."""
        result = {}
        for key, param in self.flight_info.items():
            if 'flight_nr' in key:
                index = key.split('_')[-1]
                result[f"FlightNr{index}: {param.readable_value}"] = param.arinc_value
            elif 'city_pair' in key:
                index = key.split('_')[-1]
                result[f"CityPair{index}: {param.readable_value}"] = param.arinc_value
        return result
