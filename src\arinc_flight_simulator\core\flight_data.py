"""
Flight Data Structure Module

This module defines unified data structures for storing flight parameters
with both readable values and correctly formatted ARINC 429 values.
"""

from dataclasses import dataclass
from typing import Dict, Any, Union


# ARINC 429 encoding parameters for different parameter types
ARINC_ENCODING_PARAMS = {
    'time_to_go':           {'type': 'bcd', 'sig_bits': 4, 'resolution': 0.1},      # 5 digits, 1 second resolution (99999 sec)
    'latitude':             {'type': 'bnr', 'sig_bits': 20, 'range_max': 180.0},       # 5 digits, 0.001° resolution (±90.000°)
    'longitude':            {'type': 'bnr', 'sig_bits': 20, 'range_max': 180.0},      # 6 digits, 0.001° resolution (±180.000°)
    'altitude':             {'type': 'bnr', 'sig_bits': 17, 'range_max': 131072},         # 5 digits, 1 ft resolution (±99999 ft)
    'ground_speed':         {'type': 'bnr', 'sig_bits': 15, 'range_max': 4096},     # 4 digits, 0.1 knots resolution (999.9 kts)
    'true_air_speed':       {'type': 'bnr', 'sig_bits': 15, 'range_max': 2048}, # 4 digits, 0.1 knots resolution
    'heading':              {'type': 'bnr', 'sig_bits': 15, 'resolution': 180},          # 4 digits, 0.1° resolution (359.9°)
    'temperature':          {'type': 'bnr', 'sig_bits': 11, 'resolution': 512},      # 3 digits, 0.1°C resolution (±99.9°C)
    'total_flight_time':    {'type': '', 'sig_bits': 5, 'resolution': 1.0}, # 5 digits, 1 second resolution
    'distance_nm':          {'type': '', 'sig_bits': 5, 'resolution': 0.1},      # 5 digits, 0.1 nm resolution (9999.9 nm)
    'progress':             {'type': '', 'sig_bits': 3, 'resolution': 0.001},       # 3 digits, 0.001 resolution (0.999)
}


@dataclass
class ArincParameter:
    """
    Represents a single ARINC parameter with both readable and encoded values.
    
    Attributes:
        readable_value: Human-readable value (e.g., 35000.0 for altitude)
        arinc_value: ARINC 429 formatted value for PBA Pro labels
        unit: Unit of measurement (e.g., "ft", "knots", "degrees")
        description: Human-readable description
    """
    readable_value: Union[float, int, str]
    arinc_value: Union[float, int]
    unit: str = ""
    description: str = ""


@dataclass
class FlightDataSet:
    """
    Unified data structure containing all flight parameters and ARINC words.
    
    This structure eliminates redundant calculations by storing all values
    in one place with both readable and ARINC-formatted representations.
    """
    parameter_value_map = {
        'latitude': None,
        'longitude': None,
        'ground_speed': None,
        'heading': None,
        'altitude': None,
        'true_air_speed': None,
        'flight_phase': None,
        'eta_seconds': None,
        'total_flight_time': None,
        'distance_nm': None,
        'progress': None,
        'temperature': None
    }
    
    flight_info_map = {
        'flight_nr_0': None,
        'flight_nr_1': None,
        'flight_nr_2': None,
        'city_pair_0': None,
        'city_pair_1': None
    }



    # Basic flight parameters
    latitude: ArincParameter
    longitude: ArincParameter
    ground_speed: ArincParameter
    heading: ArincParameter
    altitude: ArincParameter
    indicated_air_speed: ArincParameter
    flight_phase: ArincParameter
    time_to_go_seconds: ArincParameter
    total_flight_time: ArincParameter
    distance_nm: ArincParameter
    progress: ArincParameter
    
    # Calculated parameters
    temperature: ArincParameter

    # Encoded flight information (ARINC 429 words) as individual parameters
    flight_nr_0: ArincParameter  # First 2 characters of flight number
    flight_nr_1: ArincParameter  # Characters 3-4 of flight number
    flight_nr_2: ArincParameter  # Characters 5-6 of flight number
    city_pair_0: ArincParameter  # First 3 characters of city pair
    city_pair_1: ArincParameter  # Characters 4-6 of city pair

    # Label mapping for PBA Pro
    label_values: Dict[str, Union[float, int]]
    
    @classmethod
    def create_from_flight_data(cls, 
                               flight_data: Dict[str, Any], 
                               flight_number: str, 
                               city_pair: str,
                               label_mapping: Dict[str, Any],
                               config_manager) -> 'FlightDataSet':
        """
        Create a FlightDataSet from raw flight data and configuration.
        
        Args:
            flight_data: Raw flight simulation data
            flight_number: Flight number for encoding
            city_pair: City pair for encoding
            label_mapping: Label configuration mapping
            config_manager: Configuration manager instance
            
        Returns:
            FlightDataSet with all values calculated once
        """
        from ..arinc.utilities import encode_flight_info, encode_arinc
        
        # Calculate temperature once
        temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2
        
        # Generate ARINC words once
        arinc_words = encode_flight_info(flight_number, city_pair)

        for key, value in ARINC_ENCODING_PARAMS.items():
            arinc_words[key] = value & 0x1FFFFF  # Extract data bits (19 bits for BCD/BNR)
        
        # Create ArincParameter objects for all flight data with BCD or BNR encoding
        lat_bnr_word = encode_arinc(flight_data["Latitude_of_aircraft"], ARINC_ENCODING_PARAMS['latitude']['sig_bits'], ARINC_ENCODING_PARAMS['latitude']['range_max'])
        latitude = ArincParameter(
            readable_value=flight_data["Latitude_of_aircraft"],
            arinc_value=lat_bnr_word & 0x1FFFFF,  # Extract data bits (19 bits for BNR)
            unit="degrees",
            description="Aircraft Latitude"
        )

        lon_bnr_word = encode_arinc(flight_data["Longitude_of_aircraft"], ARINC_ENCODING_PARAMS['longitude']['sig_bits'], ARINC_ENCODING_PARAMS['longitude']['range_max'])
        longitude = ArincParameter(
            readable_value=flight_data["Longitude_of_aircraft"],
            arinc_value=lon_bnr_word & 0x1FFFFF,  # Extract data bits (19 bits for BNR)
            unit="degrees",
            description="Aircraft Longitude"
        )

        gs_bnr_word = encode_bnr_arinc(flight_data["Ground_Speed_knots"], ARINC_ENCODING_PARAMS['ground_speed']['sig_bits'], ARINC_ENCODING_PARAMS['ground_speed']['range_max'])
        ground_speed = ArincParameter(
            readable_value=flight_data["Ground_Speed_knots"],
            arinc_value=gs_bnr_word & 0x1FFFFF,  # Extract data bits (19 bits for BNR)
            unit="knots",
            description="Ground Speed"
        )

        hdg_bnr_word = encode_bcd_arinc(flight_data["Heading_degrees"], ARINC_ENCODING_PARAMS['heading']['sig_bits'], ARINC_ENCODING_PARAMS['heading']['resolution'])
        heading = ArincParameter(
            readable_value=flight_data["Heading_degrees"],
            arinc_value=hdg_bnr_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="degrees",
            description="Aircraft Heading"
        )
        
        alt_params = ARINC_ENCODING_PARAMS['altitude']
        alt_bcd_word = encode_bcd_arinc(
            flight_data["Altitude_of_aircraft_ft"],
            alt_params['sig_bits'],
            alt_params['resolution']
        )
        altitude = ArincParameter(
            readable_value=flight_data["Altitude_of_aircraft_ft"],
            arinc_value=alt_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="ft",
            description="Aircraft Altitude"
        )

        ias_params = ARINC_ENCODING_PARAMS['true_air_speed']
        ias_bcd_word = encode_bcd_arinc(
            flight_data["True_Air_Speed_knots"],
            ias_params['sig_bits'],
            ias_params['resolution']
        )

        indicated_air_speed = ArincParameter(
            readable_value=flight_data["True_Air_Speed_knots"],
            arinc_value=ias_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="knots",
            description="True Air Speed"
        )

        # Flight phase is a string, so we'll use a simple hash for ARINC value
        flight_phase = ArincParameter(
            readable_value=flight_data["Flight_Phase"],
            arinc_value=hash(flight_data["Flight_Phase"]) & 0xFFFF,  # 16-bit hash
            unit="",
            description="Flight Phase"
        )

        eta_params = ARINC_ENCODING_PARAMS['time_to_go']
        eta_bcd_word = encode_bcd_arinc(
            flight_data["ETA_seconds"],
            eta_params['sig_bits'],
            eta_params['resolution']
        )
        eta_seconds = ArincParameter(
            readable_value=flight_data["ETA_seconds"],
            arinc_value=eta_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="seconds",
            description="Estimated Time of Arrival"
        )

        tft_params = ARINC_ENCODING_PARAMS['total_flight_time']
        tft_bcd_word = encode_bcd_arinc(
            flight_data["Total_Flight_Time_seconds"],
            tft_params['sig_bits'],
            tft_params['resolution']
        )
        total_flight_time = ArincParameter(
            readable_value=flight_data["Total_Flight_Time_seconds"],
            arinc_value=tft_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="seconds",
            description="Total Flight Time"
        )

        dist_params = ARINC_ENCODING_PARAMS['distance_nm']
        dist_bcd_word = encode_bcd_arinc(
            flight_data["Distance_nm"],
            dist_params['sig_bits'],
            dist_params['resolution']
        )
        distance_nm = ArincParameter(
            readable_value=flight_data["Distance_nm"],
            arinc_value=dist_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="nm",
            description="Distance to Destination"
        )

        prog_params = ARINC_ENCODING_PARAMS['progress']
        prog_bcd_word = encode_bcd_arinc(
            flight_data["progress"],
            prog_params['sig_bits'],
            prog_params['resolution']
        )
        progress = ArincParameter(
            readable_value=flight_data["progress"],
            arinc_value=prog_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="",
            description="Flight Progress"
        )

        temp_params = ARINC_ENCODING_PARAMS['temperature']
        temp_bcd_word = encode_bcd_arinc(
            temperature_value,
            temp_params['sig_bits'],
            temp_params['resolution']
        )
        temperature = ArincParameter(
            readable_value=temperature_value,
            arinc_value=temp_bcd_word & 0x1FFFFF,  # Extract data bits (19 bits for BCD)
            unit="°C",
            description="Static Air Temperature"
        )

        # Create individual ArincParameter objects for flight info words
        # Parse the flight number and city pair from the encoded words
        flight_number_padded = flight_number.ljust(6)
        city_pair_padded = city_pair.ljust(6)

        # Extract individual ARINC words and create parameters
        flight_info_params = {}
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                index = int(key.split(":")[0].replace("FlightNr", ""))
                chars = flight_number_padded[index*2:(index*2)+2]
                flight_info_params[f"flight_nr_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"Flight Number Part {index} ({chars.strip()})"
                )
            elif "CityPair" in key:
                index = int(key.split(":")[0].replace("CityPair", ""))
                chars = city_pair_padded[index*3:(index*3)+3]
                flight_info_params[f"city_pair_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"City Pair Part {index} ({chars.strip()})"
                )

        # Create extended flight data for label mapping
        flight_data_with_extras = flight_data.copy()
        flight_data_with_extras["Static_Air_Temperature"] = temperature_value
        
        # Add encoded flight info values
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                flight_data_with_extras[f"FlightNr{flight_nr_index}"] = value
            elif "CityPair" in key:
                city_pair_index = key.split(":")[0].replace("CityPair", "")
                flight_data_with_extras[f"CityPair{city_pair_index}"] = value
        
        # Generate label values for PBA Pro
        label_values = {}
        for label_name, (_, _, simulated_value) in label_mapping.items():
            flight_data_key = config_manager.get_flight_data_key(simulated_value)
            if flight_data_key in flight_data_with_extras:
                label_values[label_name] = flight_data_with_extras[flight_data_key]
            else:
                label_values[label_name] = 0.0
        
        return cls(
            latitude=[latitude],
            longitude=longitude,
            ground_speed=ground_speed,
            heading=heading,
            altitude=altitude,
            indicated_air_speed=indicated_air_speed,
            flight_phase=flight_phase,
            eta_seconds=eta_seconds,
            total_flight_time=total_flight_time,
            distance_nm=distance_nm,
            progress=progress,
            temperature=temperature,
            flight_nr_0=flight_info_params.get('flight_nr_0', ArincParameter("", 0, "", "Flight Number Part 0")),
            flight_nr_1=flight_info_params.get('flight_nr_1', ArincParameter("", 0, "", "Flight Number Part 1")),
            flight_nr_2=flight_info_params.get('flight_nr_2', ArincParameter("", 0, "", "Flight Number Part 2")),
            city_pair_0=flight_info_params.get('city_pair_0', ArincParameter("", 0, "", "City Pair Part 0")),
            city_pair_1=flight_info_params.get('city_pair_1', ArincParameter("", 0, "", "City Pair Part 1")),
            label_values=label_values
        )
    
    def get_parameter_by_name(self, name: str) -> ArincParameter:
        """Get a parameter by its name."""
        parameter_map = {self.parameter_value_map, self.flight_info_map}
        return parameter_map.get(name.lower())

    def get_flight_info_words_dict(self) -> Dict[str, int]:
        """Get flight info words as a dictionary for backward compatibility."""
        return {
            f"FlightNr0: {self.flight_info_map['flight_nr_0'].readable_value}": self.flight_info_map['flight_nr_0'].arinc_value,
            f"FlightNr0: {self.flight_info_map['flight_nr_1'].readable_value}": self.flight_info_map['flight_nr_1'].arinc_value,
            f"FlightNr0: {self.flight_info_map['flight_nr_0'].readable_value}": self.flight_info_map['flight_nr_0'].arinc_value,
            f"FlightNr0: {self.flight_info_map['city_pair_0'].readable_value}": self.flight_info_map['city_pair_0'].arinc_value,
            f"FlightNr0: {self.flight_info_map['city_pair_2'].readable_value}": self.flight_info_map['city_pair_2'].arinc_value,
        }
